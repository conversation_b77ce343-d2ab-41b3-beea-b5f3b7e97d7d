permissions:
  contents: write

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

on:
  pull_request:
    paths:
      - '.github/workflows/release.yml'
  push:
    branches:
      - 'master'
      - 'v1.16'
      - 'v1.17'
      - 'v1.18'
      - 'v2.0'
      - 'v2.1'
      - 'v2.2'
    tags:
      - 'v*'
  workflow_dispatch:

env:
  CARGO_TERM_COLOR: always

jobs:
  release:
    strategy:
      matrix:
        os: [ubuntu-20.04, ubuntu-22.04]
    runs-on: ["${{ matrix.os }}"]
    steps:
      - name: Maximize build space
        uses: easimon/maximize-build-space@v10
        with:
          root-reserve-mb: 4096
          remove-dotnet: 'true'
          remove-android: 'true'
          remove-haskell: 'true'
          remove-codeql: 'true'

      - uses: actions/checkout@v4

      - uses: actions/cache@v4
        with:
          path: |
              ~/.cargo/bin/
              ~/.cargo/registry/index/
              ~/.cargo/registry/cache/
              ~/.cargo/git/db/
              ./target
          key: v0001-${{ matrix.os }}-rust-${{ hashFiles('rust-toolchain.toml') }}-${{ hashFiles('**/Cargo.lock') }}
          restore-keys: |
            v0001-${{ matrix.os }}-rust-${{ hashFiles('rust-toolchain.toml') }}

      - name: Set rust version
        run: |
          RUST_VERSION="$(grep -oP 'channel = "\K\d\.\d+\.\d+(?=")' rust-toolchain.toml)"
          echo "RUST_STABLE=$RUST_VERSION" | tee -a $GITHUB_ENV

      - name: Set env vars
        run: |
          source ci/env.sh
          echo "GEYSER_PLUGIN_NAME=$plugin_name" | tee -a $GITHUB_ENV
          echo "GEYSER_PLUGIN_LIB=lib${plugin_lib_name}" | tee -a $GITHUB_ENV

      - uses: dtolnay/rust-toolchain@stable
        with:
          toolchain: ${{ env.RUST_STABLE }}

      - name: Install dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y libsasl2-dev protobuf-compiler

      - name: Check Solana version
        run: |
          echo "CI_TAG=$(ci/getTag.sh)" >> "$GITHUB_ENV"
          echo "CI_OS_NAME=linux" >> "$GITHUB_ENV"

          SOLANA_VERSION="$(./ci/solana-version.sh)"
          echo "SOLANA_VERSION=$SOLANA_VERSION" >> "$GITHUB_ENV"

      - name: Create build name
        run: |
          echo "BUILD_NAME=$CI_TAG" >> "$GITHUB_ENV"

      - name: Build release tarball
        run: ./ci/create-tarball.sh

      - name: Deleteing directories to avoid upload conflict
        run: |
          rm -rf \
            target/release/client.d \
            target/release/config-check.d

      - name: Remove debug information
        run: |
          strip \
            target/release/client \
            target/release/config-check

      - name: Rename binaries
        run: |
          mv target/release/client target/release/client-${{ matrix.os }}
          mv target/release/config-check target/release/config-check-${{ matrix.os }}

      - name: Rename lib for ubuntu22 release
        if: matrix.os == 'ubuntu-22.04'
        run: |
          mv ${{ env.GEYSER_PLUGIN_NAME }}-release-x86_64-unknown-linux-gnu.tar.bz2 ${{ env.GEYSER_PLUGIN_NAME }}-release22-x86_64-unknown-linux-gnu.tar.bz2
          mv ${{ env.GEYSER_PLUGIN_NAME }}-release-x86_64-unknown-linux-gnu.yml ${{ env.GEYSER_PLUGIN_NAME }}-release22-x86_64-unknown-linux-gnu.yml

      - name: Release
        if: startsWith(github.ref, 'refs/tags/')
        uses: softprops/action-gh-release@v2
        with:
          tag_name: ${{ env.BUILD_NAME }}
          body: |
            ${{ env.GEYSER_PLUGIN_NAME }} ${{ env.CI_TAG }}
            solana ${{ env.SOLANA_VERSION }}
            rust ${{ env.RUST_STABLE }}
          files: |
            ${{ env.GEYSER_PLUGIN_NAME }}-release*
            yellowstone-grpc-proto/proto/*.proto
            target/release/client*
            target/release/config-check*

      - uses: actions/upload-artifact@v4
        with:
          name: yellowstone-grpc-${{ github.sha }}-${{ matrix.os }}
          path: |
            ${{ env.GEYSER_PLUGIN_NAME }}-release*
            yellowstone-grpc-proto/proto/*.proto
            target/release/client*
            target/release/config-check*
