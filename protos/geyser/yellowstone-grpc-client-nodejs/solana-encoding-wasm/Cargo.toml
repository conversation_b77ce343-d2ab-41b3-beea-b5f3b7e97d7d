[package]
name = "yellowstone-grpc-solana-encoding-wasm"
version = "3.0.0"
authors = ["Triton One"]
edition = "2021"
homepage = "https://triton.one"
repository = "https://github.com/rpcpool/yellowstone-grpc"
license = "Apache-2.0"
keywords = ["solana"]
publish = false

[lib]
crate-type = ["cdylib"]

[dependencies]
serde_json = "1.0.86"
solana-transaction-status = "~2.2.1"
wasm-bindgen = "0.2.100"
yellowstone-grpc-proto = { path = "../../yellowstone-grpc-proto", version = "6.0.0", default-features = false, features = ["convert"] }

[workspace.lints.clippy]
clone_on_ref_ptr = "deny"
missing_const_for_fn = "deny"
trivially_copy_pass_by_ref = "deny"

[profile.release]
lto = true
codegen-units = 1

# wasm32 and spl-token-confidential-transfer-proof-generation
[patch.crates-io]
spl-token-2022_6 = { package = "spl-token-2022", version = "6.0.0", git = "https://github.com/rpcpool/token-2022.git", tag = "program@v6.0.0-wasm" }
spl-token-2022_7 = { package = "spl-token-2022", version = "7.0.0", git = "https://github.com/rpcpool/token-2022.git", tag = "program@v7.0.0-wasm" }
