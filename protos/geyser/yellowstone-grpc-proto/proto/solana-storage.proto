syntax = "proto3";

package solana.storage.ConfirmedBlock;

option go_package = "github.com/rpcpool/yellowstone-grpc/examples/golang/proto";

message ConfirmedBlock {
    string previous_blockhash = 1;
    string blockhash = 2;
    uint64 parent_slot = 3;
    repeated ConfirmedTransaction transactions = 4;
    repeated Reward rewards = 5;
    UnixTimestamp block_time = 6;
    BlockHeight block_height = 7;
    NumPartitions num_partitions = 8;
}

message ConfirmedTransaction {
    Transaction transaction = 1;
    TransactionStatusMeta meta = 2;
}

message Transaction {
    repeated bytes signatures = 1;
    Message message = 2;
}

message Message {
    MessageHeader header = 1;
    repeated bytes account_keys = 2;
    bytes recent_blockhash = 3;
    repeated CompiledInstruction instructions = 4;
    bool versioned = 5;
    repeated MessageAddressTableLookup address_table_lookups = 6;
}

message MessageHeader {
    uint32 num_required_signatures = 1;
    uint32 num_readonly_signed_accounts = 2;
    uint32 num_readonly_unsigned_accounts = 3;
}

message MessageAddressTableLookup {
    bytes account_key = 1;
    bytes writable_indexes = 2;
    bytes readonly_indexes = 3;
}

message TransactionStatusMeta {
    TransactionError err = 1;
    uint64 fee = 2;
    repeated uint64 pre_balances = 3;
    repeated uint64 post_balances = 4;
    repeated InnerInstructions inner_instructions = 5;
    bool inner_instructions_none = 10;
    repeated string log_messages = 6;
    bool log_messages_none = 11;
    repeated TokenBalance pre_token_balances = 7;
    repeated TokenBalance post_token_balances = 8;
    repeated Reward rewards = 9;
    repeated bytes loaded_writable_addresses = 12;
    repeated bytes loaded_readonly_addresses = 13;
    ReturnData return_data = 14;
    bool return_data_none = 15;

    // Sum of compute units consumed by all instructions.
    // Available since Solana v1.10.35 / v1.11.6.
    // Set to `None` for txs executed on earlier versions.
    optional uint64 compute_units_consumed = 16;
}

message TransactionError {
    bytes err = 1;
}

message InnerInstructions {
    uint32 index = 1;
    repeated InnerInstruction instructions = 2;
}

message InnerInstruction {
    uint32 program_id_index = 1;
    bytes accounts = 2;
    bytes data = 3;

    // Invocation stack height of an inner instruction.
    // Available since Solana v1.14.6
    // Set to `None` for txs executed on earlier versions.
    optional uint32 stack_height = 4;
}

message CompiledInstruction {
    uint32 program_id_index = 1;
    bytes accounts = 2;
    bytes data = 3;
}

message TokenBalance {
    uint32 account_index = 1;
    string mint = 2;
    UiTokenAmount ui_token_amount = 3;
    string owner = 4;
    string program_id = 5;
}

message UiTokenAmount {
    double ui_amount = 1;
    uint32 decimals = 2;
    string amount = 3;
    string ui_amount_string = 4;
}

message ReturnData {
    bytes program_id = 1;
    bytes data = 2;
}

enum RewardType {
    Unspecified = 0;
    Fee = 1;
    Rent = 2;
    Staking = 3;
    Voting = 4;
}

message Reward {
    string pubkey = 1;
    int64 lamports = 2;
    uint64 post_balance = 3;
    RewardType reward_type = 4;
    string commission = 5;
}

message Rewards {
  repeated Reward rewards = 1;
  NumPartitions num_partitions = 2;
}

message UnixTimestamp {
    int64 timestamp = 1;
}

message BlockHeight {
    uint64 block_height = 1;
}

message NumPartitions {
    uint64 num_partitions = 1;
}
