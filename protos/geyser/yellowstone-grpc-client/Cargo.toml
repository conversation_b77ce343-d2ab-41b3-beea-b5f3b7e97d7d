[package]
name = "yellowstone-grpc-client"
version = "6.1.0"
authors = { workspace = true }
edition = { workspace = true }
description = "Yellowstone gRPC Geyser Simple Client"
homepage = { workspace = true }
repository = { workspace = true }
license = "Apache-2.0"
keywords = { workspace = true }
publish = true

[dependencies]
bytes = { workspace = true }
futures = { workspace = true }
thiserror ={ workspace = true }
tonic = { workspace = true, features = ["tls", "tls-roots"] }
tonic-health = { workspace = true }
yellowstone-grpc-proto = { workspace = true, features = ["tonic", "tonic-compression"] }

[dev-dependencies]
tokio = { workspace = true, features = ["rt-multi-thread", "macros"] }

[lints]
workspace = true
