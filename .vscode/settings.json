{"editor.formatOnSave": true, "editor.formatOnPaste": true, "editor.formatOnType": true, "editor.codeActionsOnSave": {"source.fixAll": "explicit", "source.organizeImports": "explicit"}, "editor.rulers": [100], "editor.tabSize": 4, "editor.insertSpaces": true, "editor.detectIndentation": false, "editor.trimAutoWhitespace": true, "files.trimTrailingWhitespace": true, "files.insertFinalNewline": true, "files.trimFinalNewlines": true, "rust-analyzer.checkOnSave.command": "clippy", "rust-analyzer.checkOnSave.allTargets": true, "rust-analyzer.checkOnSave.extraArgs": ["--all-features", "--", "-W", "clippy::pedantic", "-W", "clippy::nursery", "-A", "clippy::module_name_repetitions"], "rust-analyzer.cargo.buildScripts.enable": true, "rust-analyzer.completion.autoimport.enable": true, "rust-analyzer.completion.autoself.enable": true, "rust-analyzer.diagnostics.enable": true, "rust-analyzer.diagnostics.disabled": ["unresolved-proc-macro"], "rust-analyzer.hover.actions.enable": true, "rust-analyzer.hover.actions.implementations.enable": true, "rust-analyzer.hover.actions.references.enable": true, "rust-analyzer.hover.actions.run.enable": true, "rust-analyzer.hover.actions.debug.enable": true, "rust-analyzer.inlayHints.enable": true, "rust-analyzer.inlayHints.bindingModeHints.enable": true, "rust-analyzer.inlayHints.chainingHints.enable": true, "rust-analyzer.inlayHints.closingBraceHints.enable": true, "rust-analyzer.inlayHints.closingBraceHints.minLines": 10, "rust-analyzer.inlayHints.discriminantHints.enable": "fieldless", "rust-analyzer.inlayHints.expressionAdjustmentHints.enable": "reborrow", "rust-analyzer.inlayHints.implicitDrops.enable": true, "rust-analyzer.inlayHints.lifetimeElisionHints.enable": "skip_trivial", "rust-analyzer.inlayHints.lifetimeElisionHints.useParameterNames": true, "rust-analyzer.inlayHints.parameterHints.enable": true, "rust-analyzer.inlayHints.reborrowHints.enable": "mutable", "rust-analyzer.inlayHints.renderColons": true, "rust-analyzer.inlayHints.typeHints.enable": true, "rust-analyzer.inlayHints.typeHints.hideClosureInitialization": false, "rust-analyzer.inlayHints.typeHints.hideNamedConstructor": false, "rust-analyzer.lens.enable": true, "rust-analyzer.lens.debug.enable": true, "rust-analyzer.lens.implementations.enable": true, "rust-analyzer.lens.references.adt.enable": true, "rust-analyzer.lens.references.enumVariant.enable": true, "rust-analyzer.lens.references.method.enable": true, "rust-analyzer.lens.references.trait.enable": true, "rust-analyzer.lens.run.enable": true, "rust-analyzer.procMacro.enable": true, "rust-analyzer.procMacro.ignored": {}, "rust-analyzer.runnables.command": "cargo", "rust-analyzer.rustfmt.extraArgs": [], "rust-analyzer.rustfmt.overrideCommand": null, "rust-analyzer.rustfmt.rangeFormatting.enable": false, "rust-analyzer.workspace.symbol.search.scope": "workspace_and_dependencies", "rust-analyzer.workspace.symbol.search.kind": "only_types", "rust-analyzer.workspace.symbol.search.limit": 128, "[rust]": {"editor.defaultFormatter": "rust-lang.rust-analyzer", "editor.formatOnSave": true, "editor.semanticHighlighting.enabled": true}, "[toml]": {"editor.defaultFormatter": "tamasfe.even-better-toml", "editor.formatOnSave": true}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "[jsonc]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "[markdown]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "editor.wordWrap": "on"}, "[yaml]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "files.associations": {"*.toml": "toml", "Cargo.lock": "toml", "clippy.toml": "toml", "rustfmt.toml": "toml", "rust-toolchain.toml": "toml"}, "files.exclude": {"**/target": true, "**/.git": true, "**/.DS_Store": true, "**/node_modules": true, "**/*.rs.bk": true}, "search.exclude": {"**/target": true, "**/node_modules": true, "**/.git": true}, "terminal.integrated.env.osx": {"RUST_BACKTRACE": "1"}, "terminal.integrated.env.linux": {"RUST_BACKTRACE": "1"}, "terminal.integrated.env.windows": {"RUST_BACKTRACE": "1"}, "git.ignoreLimitWarning": true, "explorer.fileNesting.enabled": true, "explorer.fileNesting.expand": false, "explorer.fileNesting.patterns": {"Cargo.toml": "Cargo.lock", "*.rs": "$(capture).rs.bk"}, "problems.decorations.enabled": true, "errorLens.enabledDiagnosticLevels": ["error", "warning", "info", "hint"], "errorLens.exclude": ["rust-analyzer"], "todo-tree.general.tags": ["BUG", "HACK", "FIXME", "TODO", "XXX", "[ ]", "[x]"], "todo-tree.regex.regex": "(//|#|<!--|;|/\\*|^|^\\s*(-|\\*))\\s*($TAGS)", "workbench.colorCustomizations": {"rust_analyzer.inlayHints.foreground": "#999999", "rust_analyzer.inlayHints.background": "#00000000"}}