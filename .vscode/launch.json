{"version": "0.2.0", "configurations": [{"type": "lldb", "request": "launch", "name": "Debug executable 'helio-payment-tracker'", "cargo": {"args": ["build", "--bin=helio-payment-tracker", "--package=helio-payment-tracker"], "filter": {"name": "helio-payment-tracker", "kind": "bin"}}, "args": [], "cwd": "${workspaceFolder}", "env": {"RUST_BACKTRACE": "1", "RUST_LOG": "debug"}, "console": "integratedTerminal", "sourceLanguages": ["rust"]}, {"type": "lldb", "request": "launch", "name": "Debug unit tests in library 'helio-payment-tracker'", "cargo": {"args": ["test", "--no-run", "--lib", "--package=helio-payment-tracker"], "filter": {"name": "helio-payment-tracker", "kind": "lib"}}, "args": [], "cwd": "${workspaceFolder}", "env": {"RUST_BACKTRACE": "1", "RUST_LOG": "debug"}, "console": "integratedTerminal", "sourceLanguages": ["rust"]}, {"type": "lldb", "request": "launch", "name": "Debug integration tests", "cargo": {"args": ["test", "--no-run", "--test=*", "--package=helio-payment-tracker"]}, "args": [], "cwd": "${workspaceFolder}", "env": {"RUST_BACKTRACE": "1", "RUST_LOG": "debug"}, "console": "integratedTerminal", "sourceLanguages": ["rust"]}]}